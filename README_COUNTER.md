# P2PKH with Counter Smart Contract

This is an enhanced version of the P2PKH transaction code that includes a **Counter Smart Contract** output. The counter contract demonstrates a simple stateful smart contract that maintains a count value and allows it to be incremented.

## What's New

### Counter Smart Contract

The `CounterContract` class implements a simple smart contract with the following features:

- **State**: Stores an integer count value
- **Operation**: Allows incrementing the count by exactly 1
- **Validation**: Ensures the new count is exactly `current_count + 1`

### Script Logic

**Locking Script**: `<count> OP_DUP OP_1ADD OP_SWAP OP_EQUAL OP_VERIFY`
**Unlocking Script**: `<new_count>`

**Execution Flow**:
1. Unlocking script pushes the new count value
2. Locking script pushes the current count value
3. `OP_DUP` duplicates the current count
4. `OP_1ADD` adds 1 to the duplicated count
5. `OP_SWAP` rearranges the stack
6. `OP_EQUAL` checks if new_count equals current_count + 1
7. `OP_VERIFY` validates the result

## Usage

### 1. Test the Counter Logic (No Broadcasting)

```bash
python3 test_counter.py
```

This will demonstrate how the counter contract scripts work without actually broadcasting to the network.

### 2. Create a P2PKH Transaction with Counter Output

```bash
# Activate virtual environment
source venv/bin/activate

# Create transaction with counter smart contract
python3 venv/main.py counter
```

This creates a transaction with:
- One P2PKH output (1000 satoshis)
- One Counter contract output (546 satoshis, starting at count=0)
- One change output

### 3. Create a Regular P2PKH Transaction

```bash
# Activate virtual environment
source venv/bin/activate

# Create regular P2PKH transaction
python3 venv/main.py
```

### 4. Increment the Counter (Demonstration)

```bash
# Show how to increment counter (demonstration only)
python3 venv/main.py increment <txid> <output_index> <current_count>

# Example:
python3 venv/main.py increment abc123... 1 0
```

## Files

- `venv/main.py` - Enhanced main script with counter contract
- `test_counter.py` - Test script to demonstrate counter logic
- `README_COUNTER.md` - This documentation

## Counter Contract Details

### Locking Script Breakdown

```
<count>      # Push current count (e.g., 0)
OP_DUP       # Duplicate count: [0, 0]
OP_1ADD      # Add 1 to top: [0, 1]
OP_SWAP      # Swap top two: [1, 0]
OP_EQUAL     # Check equality with unlocking input
OP_VERIFY    # Verify result is true
```

### Security Features

1. **Increment Only**: The contract only allows incrementing by exactly 1
2. **No Decrement**: Cannot decrease the counter value
3. **No Skip**: Cannot increment by more than 1
4. **Deterministic**: The next valid count is always current + 1

### Use Cases

This counter contract demonstrates:
- **State Management**: How to maintain state in Bitcoin scripts
- **Validation Logic**: How to enforce business rules
- **Sequential Operations**: How to ensure ordered operations
- **Smart Contract Basics**: Foundation for more complex contracts

## Important Notes

1. **UTXO Updates**: Remember to update the UTXO information in the code before broadcasting
2. **Fees**: Counter transactions have slightly higher fees due to additional outputs
3. **Dust Limit**: Counter outputs use 546 satoshis (minimum dust limit)
4. **Testing**: Always test with small amounts first

## Next Steps

To fully implement counter spending:
1. Fetch transaction hex from blockchain
2. Create proper transaction inputs
3. Implement complete increment transaction
4. Add error handling for invalid increments

This implementation provides a solid foundation for building more complex stateful smart contracts on Bitcoin SV.
