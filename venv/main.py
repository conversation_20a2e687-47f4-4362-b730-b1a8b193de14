# Basic P2PKH transaction using the bsv library with Counter Smart Contract
import asyncio
from bsv import (
    PrivateKey, P2PKH, Transaction, TransactionInput, TransactionOutput,
    Script, ScriptChunk, OpCode, ScriptTemplate, UnlockingScriptTemplate
)
from bsv.broadcasters.whatsonchain import WhatsOnChainBroadcaster

# Replace with your private key (WIF format)
PRIVATE_KEY = 'L1jKaRKBCbMkMLrcqoU9hVRGP7L8xvS3MTnCvTcnoe2zMbyViG74'


class CounterContract(ScriptTemplate):
    """
    A simple counter smart contract that stores a count value and allows incrementing it.

    Locking Script: <count> OP_DUP OP_1ADD OP_SWAP OP_EQUAL OP_VERIFY
    Unlocking Script: <new_count>

    The contract verifies that the new count is exactly one more than the current count.
    """

    def __init__(self, initial_count=0):
        self.count = initial_count

    def lock(self, count=None):
        """Create a locking script for the counter contract."""
        if count is None:
            count = self.count

        # For small numbers (0-16), use OP_0 through OP_16
        # For larger numbers, we'd need to push the actual bytes
        if count == 0:
            count_op = OpCode.OP_0
        elif 1 <= count <= 16:
            count_op = getattr(OpCode, f'OP_{count}')
        else:
            # For larger numbers, we'd need to implement proper data pushing
            raise ValueError(f"Count {count} not supported in this simple implementation")

        # Create the locking script: <count> OP_DUP OP_1ADD OP_SWAP OP_EQUAL OP_VERIFY
        chunks = [
            ScriptChunk(count_op),  # Push current count
            ScriptChunk(OpCode.OP_DUP),  # Duplicate the count
            ScriptChunk(OpCode.OP_1ADD),  # Add 1 to the duplicated count
            ScriptChunk(OpCode.OP_SWAP),  # Swap so we have: <count+1> <count>
            ScriptChunk(OpCode.OP_EQUAL),  # Check if input equals count+1
            ScriptChunk(OpCode.OP_VERIFY)  # Verify the result
        ]

        return Script.from_chunks(chunks)

    def unlock(self, new_count):
        """Create an unlocking script template for the counter contract."""
        class CounterUnlockingTemplate(UnlockingScriptTemplate):
            def __init__(self, new_count):
                self.new_count = new_count

            def estimated_unlocking_byte_length(self):
                # One opcode for the count value
                return 1

            def sign(self, tx, input_index, subscript):
                # For small numbers (0-16), use OP_0 through OP_16
                if self.new_count == 0:
                    new_count_op = OpCode.OP_0
                elif 1 <= self.new_count <= 16:
                    new_count_op = getattr(OpCode, f'OP_{self.new_count}')
                else:
                    raise ValueError(f"Count {self.new_count} not supported in this simple implementation")

                # Create unlocking script: <new_count>
                chunks = [ScriptChunk(new_count_op)]
                return Script.from_chunks(chunks)

        return CounterUnlockingTemplate(new_count)


async def create_p2pkh_with_counter_transaction():
    """
    Creates a P2PKH transaction with an additional counter smart contract output.
    """
    try:
        priv_key = PrivateKey(PRIVATE_KEY)

        # Get a UTXO to spend - replace with your actual UTXO
        utxo_info = {
            'txid': '6b153c0e75d099822a36d08e014e5a1cd99fd608f5c844358d24fea9d9796e6d',
            'hex': '01000000013946863282892e9ae2e25686cb3cdfe76e719ca7979a204d3261627e8a1119cc000000006a47304402201def3d466d98f225becfa16389cf4a1a376e582c080834da38a94c91b368d31202202105eb7af84f1a9f8e4aa142bbd657c84c8b0f20268e47339cbd14933732d863412102264568573d6f64344c3626691d54d7e0f9e5aac391140a9ed96f3d3c9466c712ffffffff03e8030000000000001976a914df19bfc85e1b01dce4bfa899276047a3b7c4a20188ac22020000000000000600768b7c876900800100000000001976a914df19bfc85e1b01dce4bfa899276047a3b7c4a20188ac00000000',
            'output_index': 0
        }

        source_tx = Transaction.from_hex(utxo_info['hex'])

        tx_input = TransactionInput(
            source_transaction=source_tx,
            source_txid=utxo_info['txid'],
            source_output_index=utxo_info['output_index'],
            unlocking_script_template=P2PKH().unlock(priv_key),
        )

        # Create a regular P2PKH output
        p2pkh_output = TransactionOutput(
            locking_script=P2PKH().lock(priv_key.address()),
            satoshis=1000  # 1000 satoshis
        )

        # Create a counter smart contract output
        counter_contract = CounterContract(initial_count=0)
        counter_output = TransactionOutput(
            locking_script=counter_contract.lock(0),  # Start with count = 0
            satoshis=546  # Minimum dust limit for smart contract
        )

        # Create change output for remaining BSV
        change_output = TransactionOutput(
            locking_script=P2PKH().lock(priv_key.address()),
            change=True
        )

        # Create the transaction with P2PKH and counter outputs
        tx = Transaction([tx_input], [p2pkh_output, counter_output, change_output], version=1)

        # Set transaction fee
        tx.fee(150)  # Slightly higher fee for the additional output
        tx.sign()

        print(f"Transaction fee: {tx.get_fee()} satoshis")
        print(f"About to broadcast P2PKH + Counter transaction...")
        print(f"Counter output script: {counter_output.locking_script.to_asm()}")

        # Use WhatsOnChain broadcaster
        woc_broadcaster = WhatsOnChainBroadcaster()
        response = await tx.broadcast(broadcaster=woc_broadcaster)

        # Show the response details
        print(f"Broadcast status: {response.status}")

        # Handle different response types
        if response.status == "success":
            print("✅ Transaction successfully broadcast to WhatsOnChain!")
            print(f"Broadcast message: {response.message}")
            print(f"Broadcast txid: {response.txid}")
            print(f"Transaction ID: {tx.txid()}")
            print(f"Raw hex: {tx.hex()}")
            print(f"Counter contract can be spent with count = 1")
        else:
            print(f"❌ Broadcast failed!")
            if hasattr(response, 'code'):
                print(f"Error code: {response.code}")
            if hasattr(response, 'description'):
                print(f"Error description: {response.description}")
            print(f"Transaction ID: {tx.txid()}")
            print(f"Raw hex: {tx.hex()}")
            return None

        print(f"Transaction Address: {priv_key.address()}")
        return tx.txid()

    except Exception as e:
        print(f"Error creating P2PKH + Counter transaction: {e}")
        raise


async def increment_counter_transaction(counter_txid, counter_output_index, current_count):
    """
    Creates a transaction that spends from a counter contract and increments the count.

    Args:
        counter_txid: Transaction ID containing the counter output
        counter_output_index: Output index of the counter contract
        current_count: Current count value in the counter
    """
    try:
        priv_key = PrivateKey(PRIVATE_KEY)

        # For this example, we need the hex of the transaction containing the counter
        # In a real implementation, you'd fetch this from the blockchain
        print(f"To increment counter from {current_count} to {current_count + 1}")
        print(f"You need to provide the hex of transaction {counter_txid}")
        print("This is a demonstration of how the increment would work.")

        # Create counter contract instances
        old_counter = CounterContract(current_count)
        new_counter = CounterContract(current_count + 1)

        # Show what the scripts would look like
        print(f"Current counter locking script: {old_counter.lock(current_count).to_asm()}")
        print(f"New counter locking script: {new_counter.lock(current_count + 1).to_asm()}")
        print(f"Unlocking script needed: {current_count + 1} (as bytes)")

        return f"increment_{counter_txid}_{current_count}_to_{current_count + 1}"

    except Exception as e:
        print(f"Error creating counter increment transaction: {e}")
        raise

 #       # below is a standard P2PKH transaction

async def create_p2pkh_transaction():
    """
    Creates a basic P2PKH transaction.
    """
    try:
        priv_key = PrivateKey(PRIVATE_KEY)

        # Get a UTXO to spend - replace with your actual UTXO
        utxo_info = {
            'txid': '',
            'hex': '',
            'output_index': 1
        }

        source_tx = Transaction.from_hex(utxo_info['hex'])

        tx_input = TransactionInput(
            source_transaction=source_tx,
            source_txid=utxo_info['txid'],
            source_output_index=utxo_info['output_index'],
            unlocking_script_template=P2PKH().unlock(priv_key),
        )

        # Create a regular output
        output = TransactionOutput(
            locking_script=P2PKH().lock(priv_key.address()),
            satoshis=1000  # 1000 satoshis
        )

        # Create change output for remaining BSV
        change_output = TransactionOutput(
            locking_script=P2PKH().lock(priv_key.address()),
            change=True
        )

        # Create the transaction with P2PKH outputs
        tx = Transaction([tx_input], [output, change_output], version=1)

        # Set transaction fee
        tx.fee(100)  # 100 satoshis fee
        tx.sign()

        print(f"Transaction fee: {tx.get_fee()} satoshis")
        print(f"About to broadcast P2PKH transaction...")

        # Use WhatsOnChain broadcaster
        woc_broadcaster = WhatsOnChainBroadcaster()
        response = await tx.broadcast(broadcaster=woc_broadcaster)

        # Show the response details
        print(f"Broadcast status: {response.status}")

        # Handle different response types
        if response.status == "success":
            print("✅ Transaction successfully broadcast to WhatsOnChain!")
            print(f"Broadcast message: {response.message}")
            print(f"Broadcast txid: {response.txid}")
            print(f"Transaction ID: {tx.txid()}")
            print(f"Raw hex: {tx.hex()}")
        else:
            print(f"❌ Broadcast failed!")
            if hasattr(response, 'code'):
                print(f"Error code: {response.code}")
            if hasattr(response, 'description'):
                print(f"Error description: {response.description}")
            print(f"Transaction ID: {tx.txid()}")
            print(f"Raw hex: {tx.hex()}")
            return None

        print(f"Transaction Address: {priv_key.address()}")
        return tx.txid()

    except Exception as e:
        print(f"Error creating P2PKH transaction: {e}")
        raise

if __name__ == "__main__":
    import sys

    if len(sys.argv) > 1 and sys.argv[1] == "counter":
        # Create a P2PKH transaction with counter smart contract
        print("Creating a P2PKH transaction with Counter Smart Contract...")
        txid = asyncio.run(create_p2pkh_with_counter_transaction())
        print(f"Successfully created P2PKH + Counter transaction: {txid}")
        print("\nTo increment the counter, you would use:")
        print(f"python3 venv/main.py increment {txid} 1 0")
    elif len(sys.argv) > 1 and sys.argv[1] == "increment":
        if len(sys.argv) < 5:
            print("Usage: python3 venv/main.py increment <counter_txid> <output_index> <current_count>")
            sys.exit(1)

        counter_txid = sys.argv[2]
        output_index = int(sys.argv[3])
        current_count = int(sys.argv[4])

        print(f"Incrementing counter from {current_count} to {current_count + 1}...")
        result = asyncio.run(increment_counter_transaction(counter_txid, output_index, current_count))
        print(f"Counter increment result: {result}")
    else:
        # Create a basic P2PKH transaction
        print("Creating a basic P2PKH transaction...")
        print("Use 'python3 venv/main.py counter' to create a transaction with a counter smart contract")
        txid = asyncio.run(create_p2pkh_transaction())
        print(f"Successfully created P2PKH transaction: {txid}")


#
# source venv/bin/activate (if not in venv)
# python3 venv/main.py counter
#
# To increment the counter, you would use (txid, output_index, current_count ):
#python3 venv/main.py increment 6b153c0e75d099822a36d08e014e5a1cd99fd608f5c844358d24fea9d9796e6d 1 0