# Basic P2PKH transaction using the bsv library
import asyncio
from bsv import (
    PrivateKey, P2PKH, Transaction, TransactionInput, TransactionOutput
)
from bsv.broadcasters.whatsonchain import WhatsOnChainBroadcaster

# Replace with your private key (WIF format)
PRIVATE_KEY = 'L3GwUdGZDuHEHb7UdvhfYQdyY6PY35o11kAokzyTx1izAYNWF4BP'

async def create_p2pkh_transaction():
    """
    Creates a basic P2PKH transaction.
    """
    try:
        priv_key = PrivateKey(PRIVATE_KEY)

        # Get a UTXO to spend - replace with your actual UTXO
        utxo_info = {
            'txid': 'ad84c057123023b67359fb32accecab339e7dbe4931a77e5a2a989e1f51f4467',
            'hex': '01000000017232c67cc772947c966506bfc6f4630054871d534e729a8c67b838f0f8baf715010000006a47304402207612b45a72b9b6cac96dc088cbc5885b87612443833b4db06d21fcb228ed91c202205a98bae3f8cf416164d2b74e7f6205f3afe3893b4cb6600323cd85cef0bc302c412103d7d48db1db74f3c5b39ccd7f6294e5dead66516cba91866dde2f745d2a94cdb8ffffffff02e8030000000000001976a914bc05604acc8c8081af78e2bcf422cc974e18d4d688ac70750100000000001976a914bc05604acc8c8081af78e2bcf422cc974e18d4d688ac00000000',
            'output_index': 1
        }

        source_tx = Transaction.from_hex(utxo_info['hex'])

        tx_input = TransactionInput(
            source_transaction=source_tx,
            source_txid=utxo_info['txid'],
            source_output_index=utxo_info['output_index'],
            unlocking_script_template=P2PKH().unlock(priv_key),
        )

        # Create a regular output
        output = TransactionOutput(
            locking_script=P2PKH().lock(priv_key.address()),
            satoshis=1000  # 1000 satoshis
        )

        # Create change output for remaining BSV
        change_output = TransactionOutput(
            locking_script=P2PKH().lock(priv_key.address()),
            change=True
        )

        # Create the transaction with P2PKH outputs
        tx = Transaction([tx_input], [output, change_output], version=1)

        # Set transaction fee
        tx.fee(100)  # 100 satoshis fee
        tx.sign()

        print(f"Transaction fee: {tx.get_fee()} satoshis")
        print(f"About to broadcast P2PKH transaction...")

        # Use WhatsOnChain broadcaster
        woc_broadcaster = WhatsOnChainBroadcaster()
        response = await tx.broadcast(broadcaster=woc_broadcaster)

        # Show the response details
        print(f"Broadcast status: {response.status}")

        # Handle different response types
        if response.status == "success":
            print("✅ Transaction successfully broadcast to WhatsOnChain!")
            print(f"Broadcast message: {response.message}")
            print(f"Broadcast txid: {response.txid}")
            print(f"Transaction ID: {tx.txid()}")
            print(f"Raw hex: {tx.hex()}")
        else:
            print(f"❌ Broadcast failed!")
            if hasattr(response, 'code'):
                print(f"Error code: {response.code}")
            if hasattr(response, 'description'):
                print(f"Error description: {response.description}")
            print(f"Transaction ID: {tx.txid()}")
            print(f"Raw hex: {tx.hex()}")
            return None

        print(f"Transaction Address: {priv_key.address()}")
        return tx.txid()

    except Exception as e:
        print(f"Error creating P2PKH transaction: {e}")
        raise

if __name__ == "__main__":
    # Create a basic P2PKH transaction
    print("Creating a P2PKH transaction...")
    txid = asyncio.run(create_p2pkh_transaction())
    print(f"Successfully created P2PKH transaction: {txid}")

# update lines 20,21 (txid,hex),
# maybe update line 22 (index - both indexes spendable?) and run again
# maybe update line 37 (sats) and line 50 (fee).
#
# seems to work not only good, but perfect.
#
# source venv/bin/activate (if not in venv)
# python3 venv/main.py
#